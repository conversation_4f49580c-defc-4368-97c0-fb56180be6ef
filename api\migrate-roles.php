<?php
require_once 'config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<h1>Migration: Ad<PERSON><PERSON> and <PERSON><PERSON> Roles</h1>";

try {
    $conn = getConnection();
    
    echo "<h2>1. Checking current role column...</h2>";
    
    // Check current role column definition
    $result = $conn->query("SHOW COLUMNS FROM users LIKE 'role'");
    if ($result && $result->num_rows > 0) {
        $column = $result->fetch_assoc();
        echo "<p>Current role column: " . htmlspecialchars($column['Type']) . "</p>";
        
        // Check if new roles already exist
        if (strpos($column['Type'], 'koki') !== false && strpos($column['Type'], 'kasir') !== false) {
            echo "<p style='color: orange;'>⚠️ New roles already exist in the database.</p>";
        } else {
            echo "<h2>2. Updating role column...</h2>";
            
            // Modify the role enum to include new roles
            $sql = "ALTER TABLE `users` MODIFY COLUMN `role` ENUM('admin', 'user', 'koki', 'kasir') NOT NULL DEFAULT 'user'";
            
            if ($conn->query($sql)) {
                echo "<p style='color: green;'>✓ Role column updated successfully</p>";
            } else {
                throw new Exception("Failed to update role column: " . $conn->error);
            }
        }
    } else {
        throw new Exception("Users table or role column not found");
    }
    
    echo "<h2>3. Adding sample users...</h2>";
    
    // Add sample koki user
    $password = password_hash('password', PASSWORD_DEFAULT);
    $now = date('Y-m-d H:i:s');
    
    // Check if koki user exists
    $result = $conn->query("SELECT id FROM users WHERE username = 'koki'");
    if ($result->num_rows == 0) {
        $sql = "INSERT INTO users (name, username, email, password, role, created_at, updated_at) 
                VALUES ('Koki', 'koki', '<EMAIL>', '$password', 'koki', '$now', '$now')";
        
        if ($conn->query($sql)) {
            echo "<p style='color: green;'>✓ Koki user created (username: koki, password: password)</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to create koki user: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Koki user already exists</p>";
    }
    
    // Check if kasir user exists
    $result = $conn->query("SELECT id FROM users WHERE username = 'kasir'");
    if ($result->num_rows == 0) {
        $sql = "INSERT INTO users (name, username, email, password, role, created_at, updated_at) 
                VALUES ('Kasir', 'kasir', '<EMAIL>', '$password', 'kasir', '$now', '$now')";
        
        if ($conn->query($sql)) {
            echo "<p style='color: green;'>✓ Kasir user created (username: kasir, password: password)</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to create kasir user: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Kasir user already exists</p>";
    }
    
    echo "<h2>4. Verification</h2>";
    
    // Show updated column definition
    $result = $conn->query("SHOW COLUMNS FROM users LIKE 'role'");
    if ($result && $result->num_rows > 0) {
        $column = $result->fetch_assoc();
        echo "<p>Updated role column: " . htmlspecialchars($column['Type']) . "</p>";
    }
    
    // Show all users
    $result = $conn->query("SELECT id, name, username, email, role, created_at FROM users ORDER BY role, id");
    if ($result && $result->num_rows > 0) {
        echo "<h3>Current Users:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Username</th><th>Email</th><th>Role</th><th>Created</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['username']) . "</td>";
            echo "<td>" . htmlspecialchars($row['email']) . "</td>";
            echo "<td><strong>" . htmlspecialchars($row['role']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($row['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    $conn->close();
    
    echo "<h2>5. Migration Complete!</h2>";
    echo "<p style='color: green; font-weight: bold;'>✓ Migration completed successfully!</p>";
    echo "<p>You can now use the following test accounts:</p>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> username: admin, password: password</li>";
    echo "<li><strong>Koki:</strong> username: koki, password: password</li>";
    echo "<li><strong>Kasir:</strong> username: kasir, password: password</li>";
    echo "</ul>";
    
    echo "<p><a href='../'>← Back to Application</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Migration failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
}
?>
