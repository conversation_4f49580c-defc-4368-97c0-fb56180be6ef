<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set headers
header('Content-Type: text/html; charset=utf-8');

require_once 'config.php';

echo "<h1>🔧 Perbaikan Sistem Login & Register</h1>";

try {
    // Connect to database
    $conn = getConnection();
    echo "<p style='color: green;'>✓ Koneksi database berhasil</p>";
    
    // 1. Check and create users table
    echo "<h2>1. Memeriksa Tabel Users</h2>";
    $result = $conn->query("SHOW TABLES LIKE 'users'");
    if ($result->num_rows === 0) {
        echo "<p style='color: orange;'>⚠ Tabel users tidak ditemukan, membuat tabel...</p>";
        
        $sql = "CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'koki', 'kasir', 'user') NOT NULL DEFAULT 'user',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($sql)) {
            echo "<p style='color: green;'>✓ Tabel users berhasil dibuat</p>";
        } else {
            throw new Exception('Gagal membuat tabel users: ' . $conn->error);
        }
    } else {
        echo "<p style='color: green;'>✓ Tabel users sudah ada</p>";
        
        // Check if role column supports 'user'
        $result = $conn->query("SHOW COLUMNS FROM users LIKE 'role'");
        if ($result->num_rows > 0) {
            $column = $result->fetch_assoc();
            if (strpos($column['Type'], 'user') === false) {
                echo "<p style='color: orange;'>⚠ Menambahkan role 'user' ke kolom role...</p>";
                $conn->query("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'koki', 'kasir', 'user') NOT NULL DEFAULT 'user'");
                echo "<p style='color: green;'>✓ Role 'user' berhasil ditambahkan</p>";
            }
        }
    }
    
    // 2. Create/Update test users
    echo "<h2>2. Membuat/Memperbarui User Test</h2>";
    
    $testUsers = [
        [
            'name' => 'Administrator',
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => 'password',
            'role' => 'admin'
        ],
        [
            'name' => 'Koki Restaurant',
            'username' => 'koki',
            'email' => '<EMAIL>',
            'password' => 'password',
            'role' => 'koki'
        ],
        [
            'name' => 'Kasir Restaurant',
            'username' => 'kasir',
            'email' => '<EMAIL>',
            'password' => 'password',
            'role' => 'kasir'
        ],
        [
            'name' => 'User Biasa',
            'username' => 'user',
            'email' => '<EMAIL>',
            'password' => 'password',
            'role' => 'user'
        ]
    ];
    
    foreach ($testUsers as $user) {
        // Check if user exists
        $stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->bind_param("s", $user['username']);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $hashedPassword = password_hash($user['password'], PASSWORD_DEFAULT);
        
        if ($result->num_rows > 0) {
            // Update existing user
            $stmt = $conn->prepare("UPDATE users SET name = ?, email = ?, password = ?, role = ?, updated_at = NOW() WHERE username = ?");
            $stmt->bind_param("sssss", $user['name'], $user['email'], $hashedPassword, $user['role'], $user['username']);
            
            if ($stmt->execute()) {
                echo "<p style='color: blue;'>↻ User diperbarui: " . $user['username'] . " (" . $user['role'] . ")</p>";
            } else {
                echo "<p style='color: red;'>✗ Gagal memperbarui user: " . $user['username'] . "</p>";
            }
        } else {
            // Create new user
            $stmt = $conn->prepare("INSERT INTO users (name, username, email, password, role, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())");
            $stmt->bind_param("sssss", $user['name'], $user['username'], $user['email'], $hashedPassword, $user['role']);
            
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✓ User dibuat: " . $user['username'] . " (" . $user['role'] . ")</p>";
            } else {
                echo "<p style='color: red;'>✗ Gagal membuat user: " . $user['username'] . "</p>";
            }
        }
    }
    
    // 3. Test login functionality
    echo "<h2>3. Testing Login</h2>";
    $testUsername = 'koki';
    $testPassword = 'password';
    
    $stmt = $conn->prepare("SELECT id, name, username, email, password, role FROM users WHERE username = ?");
    $stmt->bind_param("s", $testUsername);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        if (password_verify($testPassword, $user['password'])) {
            echo "<p style='color: green;'>✓ Test login berhasil untuk user: " . $user['username'] . "</p>";
        } else {
            echo "<p style='color: red;'>✗ Test login gagal - password tidak cocok</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Test login gagal - user tidak ditemukan</p>";
    }
    
    // 4. Show current users
    echo "<h2>4. Daftar User Saat Ini</h2>";
    $result = $conn->query("SELECT id, name, username, email, role, created_at FROM users ORDER BY role, id");
    
    if ($result && $result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background-color: #f2f2f2;'>";
        echo "<th style='padding: 10px;'>ID</th>";
        echo "<th style='padding: 10px;'>Nama</th>";
        echo "<th style='padding: 10px;'>Username</th>";
        echo "<th style='padding: 10px;'>Email</th>";
        echo "<th style='padding: 10px;'>Role</th>";
        echo "<th style='padding: 10px;'>Dibuat</th>";
        echo "</tr>";
        
        while ($row = $result->fetch_assoc()) {
            $roleColor = '';
            switch($row['role']) {
                case 'admin': $roleColor = 'background-color: #d4edda; color: #155724;'; break;
                case 'koki': $roleColor = 'background-color: #fff3cd; color: #856404;'; break;
                case 'kasir': $roleColor = 'background-color: #cce5ff; color: #004085;'; break;
                case 'user': $roleColor = 'background-color: #f8f9fa; color: #495057;'; break;
            }
            
            echo "<tr>";
            echo "<td style='padding: 8px; text-align: center;'>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td style='padding: 8px; font-weight: bold;'>" . htmlspecialchars($row['username']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['email']) . "</td>";
            echo "<td style='padding: 8px; $roleColor font-weight: bold; text-align: center;'>" . strtoupper(htmlspecialchars($row['role'])) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    $conn->close();
    
    // 5. Final status and instructions
    echo "<h2>5. Status Akhir</h2>";
    echo "<div style='background-color: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>🎉 Sistem Login & Register Berhasil Diperbaiki!</h3>";
    echo "<p><strong>Kredensial untuk testing:</strong></p>";
    echo "<ul style='margin: 10px 0;'>";
    echo "<li><strong>Admin:</strong> username: <code>admin</code>, password: <code>password</code></li>";
    echo "<li><strong>Koki:</strong> username: <code>koki</code>, password: <code>password</code></li>";
    echo "<li><strong>Kasir:</strong> username: <code>kasir</code>, password: <code>password</code></li>";
    echo "<li><strong>User:</strong> username: <code>user</code>, password: <code>password</code></li>";
    echo "</ul>";
    echo "<p><strong>Untuk register:</strong> Gunakan email dan username yang belum ada di database.</p>";
    echo "</div>";
    
    echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>📋 Langkah Selanjutnya:</h4>";
    echo "<ol>";
    echo "<li>Coba login dengan salah satu akun di atas</li>";
    echo "<li>Coba daftar akun baru dengan email/username yang berbeda</li>";
    echo "<li>Periksa apakah redirect berdasarkan role berfungsi dengan benar</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='margin-top: 30px; text-align: center;'>";
    echo "<a href='../login' style='background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 0 10px; display: inline-block;'>🔑 Coba Login</a>";
    echo "<a href='../register' style='background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 0 10px; display: inline-block;'>📝 Coba Register</a>";
    echo "<a href='test-auth.html' style='background-color: #ffc107; color: black; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 0 10px; display: inline-block;'>🧪 Test API</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ Error:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>
