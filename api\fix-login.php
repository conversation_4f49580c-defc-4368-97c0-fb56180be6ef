<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set headers
header('Content-Type: text/html; charset=utf-8');

require_once 'config.php';

echo "<h1>Fix Login System</h1>";

try {
    // Connect to database
    $conn = getConnection();
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Check if users table exists
    $result = $conn->query("SHOW TABLES LIKE 'users'");
    if ($result->num_rows === 0) {
        echo "<p style='color: red;'>✗ Users table does not exist!</p>";
        echo "<p>Creating users table...</p>";
        
        // Create users table
        $sql = "CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'koki', 'kasir') NOT NULL DEFAULT 'kasir',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if ($conn->query($sql)) {
            echo "<p style='color: green;'>✓ Users table created successfully</p>";
        } else {
            echo "<p style='color: red;'>✗ Error creating users table: " . $conn->error . "</p>";
            exit;
        }
    } else {
        echo "<p style='color: green;'>✓ Users table exists</p>";
    }
    
    // Check existing users
    $result = $conn->query("SELECT username, role FROM users");
    $existingUsers = [];
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $existingUsers[] = $row['username'];
        }
    }
    
    echo "<h2>Creating/Updating Test Users</h2>";
    
    // Define test users
    $testUsers = [
        [
            'name' => 'Administrator',
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => 'password',
            'role' => 'admin'
        ],
        [
            'name' => 'Koki',
            'username' => 'koki',
            'email' => '<EMAIL>',
            'password' => 'password',
            'role' => 'koki'
        ],
        [
            'name' => 'Kasir',
            'username' => 'kasir',
            'email' => '<EMAIL>',
            'password' => 'password',
            'role' => 'kasir'
        ]
    ];
    
    foreach ($testUsers as $user) {
        // Check if user exists
        $stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->bind_param("s", $user['username']);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            // Update existing user
            $hashedPassword = password_hash($user['password'], PASSWORD_DEFAULT);
            $stmt = $conn->prepare("UPDATE users SET name = ?, email = ?, password = ?, role = ?, updated_at = NOW() WHERE username = ?");
            $stmt->bind_param("sssss", $user['name'], $user['email'], $hashedPassword, $user['role'], $user['username']);
            
            if ($stmt->execute()) {
                echo "<p style='color: blue;'>↻ Updated user: " . $user['username'] . " (" . $user['role'] . ")</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to update user: " . $user['username'] . " - " . $stmt->error . "</p>";
            }
        } else {
            // Create new user
            $hashedPassword = password_hash($user['password'], PASSWORD_DEFAULT);
            $stmt = $conn->prepare("INSERT INTO users (name, username, email, password, role, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())");
            $stmt->bind_param("sssss", $user['name'], $user['username'], $user['email'], $hashedPassword, $user['role']);
            
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✓ Created user: " . $user['username'] . " (" . $user['role'] . ")</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create user: " . $user['username'] . " - " . $stmt->error . "</p>";
            }
        }
    }
    
    echo "<h2>Testing Login Functionality</h2>";
    
    // Test login for koki user
    $testUsername = 'koki';
    $testPassword = 'password';
    
    $stmt = $conn->prepare("SELECT id, name, username, email, password, role FROM users WHERE username = ?");
    $stmt->bind_param("s", $testUsername);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        echo "<p style='color: green;'>✓ Found user: " . $user['username'] . "</p>";
        
        if (password_verify($testPassword, $user['password'])) {
            echo "<p style='color: green;'>✓ Password verification successful for user: " . $user['username'] . "</p>";
            echo "<p><strong>Login should work now!</strong></p>";
        } else {
            echo "<p style='color: red;'>✗ Password verification failed for user: " . $user['username'] . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ User not found: " . $testUsername . "</p>";
    }
    
    $conn->close();
    
    echo "<h2>Final Status</h2>";
    echo "<div style='background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✓ Login system has been fixed!</h3>";
    echo "<p><strong>Test Credentials:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> username: <code>admin</code>, password: <code>password</code></li>";
    echo "<li><strong>Koki:</strong> username: <code>koki</code>, password: <code>password</code></li>";
    echo "<li><strong>Kasir:</strong> username: <code>kasir</code>, password: <code>password</code></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='../login' style='background-color: #007bff; color: white; padding: 12px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Go to Login Page</a>";
    echo "<a href='check-users.php' style='background-color: #28a745; color: white; padding: 12px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Check Users</a>";
    echo "<a href='test-login-form.html' style='background-color: #ffc107; color: black; padding: 12px 20px; text-decoration: none; border-radius: 5px;'>Test Login</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
