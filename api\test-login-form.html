<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .test-btn {
            background-color: #28a745;
        }
        .test-btn:hover {
            background-color: #1e7e34;
        }
        .response {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .test-accounts {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Login System</h1>
        
        <div class="test-accounts">
            <h3>Test Accounts:</h3>
            <ul>
                <li><strong>Admin:</strong> username: admin, password: password</li>
                <li><strong>Koki:</strong> username: koki, password: password</li>
                <li><strong>Kasir:</strong> username: kasir, password: password</li>
            </ul>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" value="koki" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" value="password" required>
            </div>
            
            <button type="submit">Test Login (Original API)</button>
            <button type="button" class="test-btn" onclick="testLoginAPI()">Test Login (Test API)</button>
            <button type="button" onclick="fillKoki()">Fill Koki</button>
            <button type="button" onclick="fillKasir()">Fill Kasir</button>
            <button type="button" onclick="fillAdmin()">Fill Admin</button>
        </form>
        
        <div id="response"></div>
    </div>

    <script>
        // Fill form with different accounts
        function fillKoki() {
            document.getElementById('username').value = 'koki';
            document.getElementById('password').value = 'password';
        }
        
        function fillKasir() {
            document.getElementById('username').value = 'kasir';
            document.getElementById('password').value = 'password';
        }
        
        function fillAdmin() {
            document.getElementById('username').value = 'admin';
            document.getElementById('password').value = 'password';
        }
        
        // Test with original login API
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            const data = {
                username: username,
                password: password
            };
            
            try {
                const response = await fetch('login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                document.getElementById('response').innerHTML = 
                    `<div class="response ${result.success ? 'success' : 'error'}">
                        <strong>Original API Response:</strong>\n${JSON.stringify(result, null, 2)}
                    </div>`;
                    
            } catch (error) {
                document.getElementById('response').innerHTML = 
                    `<div class="response error">
                        <strong>Error:</strong>\n${error.message}
                    </div>`;
            }
        });
        
        // Test with test login API
        async function testLoginAPI() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            const data = {
                username: username,
                password: password
            };
            
            try {
                const response = await fetch('test-login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                document.getElementById('response').innerHTML = 
                    `<div class="response ${result.success ? 'success' : 'error'}">
                        <strong>Test API Response:</strong>\n${JSON.stringify(result, null, 2)}
                    </div>`;
                    
            } catch (error) {
                document.getElementById('response').innerHTML = 
                    `<div class="response error">
                        <strong>Error:</strong>\n${error.message}
                    </div>`;
            }
        }
    </script>
</body>
</html>
