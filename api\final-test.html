<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Test - Login & Register</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
        }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .test-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #e9ecef;
        }
        .test-card h3 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            box-sizing: border-box;
            font-size: 14px;
        }
        input:focus, select:focus {
            outline: none;
            border-color: #007bff;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .response {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status-section {
            background: #e7f3ff;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            border-left: 5px solid #007bff;
        }
        .quick-actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        .quick-actions a {
            padding: 10px 20px;
            background: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 600;
            transition: background 0.3s;
        }
        .quick-actions a:hover {
            background: #545b62;
        }
        .quick-actions a.primary {
            background: #007bff;
        }
        .quick-actions a.primary:hover {
            background: #0056b3;
        }
        .quick-actions a.success {
            background: #28a745;
        }
        .quick-actions a.success:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Final Test</h1>
            <p>Test sistem login dan register yang sudah diperbaiki</p>
        </div>

        <div class="test-grid">
            <!-- Login Test -->
            <div class="test-card">
                <h3>🔑 Test Login</h3>
                <form id="loginForm">
                    <div class="form-group">
                        <label>Username:</label>
                        <input type="text" id="username" value="koki" required>
                    </div>
                    <div class="form-group">
                        <label>Password:</label>
                        <input type="password" id="password" value="password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Test Login</button>
                </form>
                <div id="loginResponse"></div>
            </div>

            <!-- Register Test -->
            <div class="test-card">
                <h3>📝 Test Register</h3>
                <form id="registerForm">
                    <div class="form-group">
                        <label>Nama:</label>
                        <input type="text" id="name" value="User Baru" required>
                    </div>
                    <div class="form-group">
                        <label>Username:</label>
                        <input type="text" id="regUsername" value="" required>
                    </div>
                    <div class="form-group">
                        <label>Email:</label>
                        <input type="email" id="email" value="" required>
                    </div>
                    <div class="form-group">
                        <label>Password:</label>
                        <input type="password" id="regPassword" value="password123" required>
                    </div>
                    <button type="submit" class="btn btn-success">Test Register</button>
                </form>
                <div id="registerResponse"></div>
            </div>
        </div>

        <div class="status-section">
            <h3>📋 Status & Instruksi</h3>
            <p><strong>Akun Test yang Tersedia:</strong></p>
            <ul>
                <li><strong>Admin:</strong> username: <code>admin</code>, password: <code>password</code></li>
                <li><strong>Koki:</strong> username: <code>koki</code>, password: <code>password</code></li>
                <li><strong>Kasir:</strong> username: <code>kasir</code>, password: <code>password</code></li>
                <li><strong>User:</strong> username: <code>user</code>, password: <code>password</code></li>
            </ul>
            
            <p><strong>Langkah Testing:</strong></p>
            <ol>
                <li>Test login dengan salah satu akun di atas</li>
                <li>Test register dengan username/email baru</li>
                <li>Jika berhasil, coba login di aplikasi React</li>
            </ol>

            <div class="quick-actions">
                <a href="../login" class="primary">🔑 Login React App</a>
                <a href="../register" class="success">📝 Register React App</a>
                <a href="check-users.php">👥 Lihat Users</a>
                <a href="fix-auth-system.php">🔧 Run Fix</a>
            </div>
        </div>
    </div>

    <script>
        // Generate random data for register
        function generateRandomData() {
            const timestamp = Date.now();
            document.getElementById('regUsername').value = `user${timestamp}`;
            document.getElementById('email').value = `user${timestamp}@example.com`;
        }

        // Test Login
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const data = {
                username: document.getElementById('username').value,
                password: document.getElementById('password').value
            };
            
            try {
                const response = await fetch('login.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                document.getElementById('loginResponse').innerHTML = 
                    `<div class="response ${result.success ? 'success' : 'error'}">
                        ${JSON.stringify(result, null, 2)}
                    </div>`;
                    
            } catch (error) {
                document.getElementById('loginResponse').innerHTML = 
                    `<div class="response error">Error: ${error.message}</div>`;
            }
        });

        // Test Register
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const data = {
                name: document.getElementById('name').value,
                username: document.getElementById('regUsername').value,
                email: document.getElementById('email').value,
                password: document.getElementById('regPassword').value
            };
            
            try {
                const response = await fetch('register.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                document.getElementById('registerResponse').innerHTML = 
                    `<div class="response ${result.success ? 'success' : 'error'}">
                        ${JSON.stringify(result, null, 2)}
                    </div>`;
                    
                if (result.success) {
                    setTimeout(generateRandomData, 2000);
                }
                    
            } catch (error) {
                document.getElementById('registerResponse').innerHTML = 
                    `<div class="response error">Error: ${error.message}</div>`;
            }
        });

        // Initialize with random data
        generateRandomData();
    </script>
</body>
</html>
