<?php
require_once 'config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<h1>Check Users in Database</h1>";

try {
    $conn = getConnection();
    
    echo "<h2>Current Users in Database:</h2>";
    
    // Get all users
    $result = $conn->query("SELECT id, name, username, email, role, created_at FROM users ORDER BY role, id");
    
    if ($result && $result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background-color: #f2f2f2;'>";
        echo "<th style='padding: 10px;'>ID</th>";
        echo "<th style='padding: 10px;'>Name</th>";
        echo "<th style='padding: 10px;'>Username</th>";
        echo "<th style='padding: 10px;'>Email</th>";
        echo "<th style='padding: 10px;'>Role</th>";
        echo "<th style='padding: 10px;'>Created</th>";
        echo "</tr>";
        
        while ($row = $result->fetch_assoc()) {
            $roleColor = '';
            switch($row['role']) {
                case 'admin': $roleColor = 'background-color: #d4edda;'; break;
                case 'koki': $roleColor = 'background-color: #fff3cd;'; break;
                case 'kasir': $roleColor = 'background-color: #cce5ff;'; break;
                default: $roleColor = 'background-color: #f8f9fa;'; break;
            }
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td style='padding: 8px; font-weight: bold;'>" . htmlspecialchars($row['username']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['email']) . "</td>";
            echo "<td style='padding: 8px; $roleColor font-weight: bold;'>" . htmlspecialchars($row['role']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h2>Test Login Credentials:</h2>";
        echo "<div style='background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>Use these credentials to login:</h3>";
        echo "<ul>";
        echo "<li><strong>Admin:</strong> Username: <code>admin</code> | Email: <code><EMAIL></code> | Password: <code>password</code></li>";
        echo "<li><strong>Koki:</strong> Username: <code>koki</code> | Email: <code><EMAIL></code> | Password: <code>password</code></li>";
        echo "<li><strong>Kasir:</strong> Username: <code>kasir</code> | Email: <code><EMAIL></code> | Password: <code>password</code></li>";
        echo "</ul>";
        echo "<p><strong>Note:</strong> You can login using either username or email.</p>";
        echo "</div>";
        
    } else {
        echo "<p style='color: red;'>No users found in database!</p>";
        echo "<p><a href='migrate-roles.php'>Run Migration Script</a> to create test users.</p>";
    }
    
    // Check role column definition
    echo "<h2>Role Column Definition:</h2>";
    $result = $conn->query("SHOW COLUMNS FROM users LIKE 'role'");
    if ($result && $result->num_rows > 0) {
        $column = $result->fetch_assoc();
        echo "<p><strong>Column Type:</strong> " . htmlspecialchars($column['Type']) . "</p>";
    }
    
    $conn->close();
    
    echo "<div style='margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;'>";
    echo "<h3>Quick Actions:</h3>";
    echo "<p><a href='../login' style='background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>Go to Login Page</a></p>";
    echo "<p><a href='migrate-roles.php' style='background-color: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>Run Migration</a></p>";
    echo "<p><a href='setup.php' style='background-color: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>Run Setup</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
