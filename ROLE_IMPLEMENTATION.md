# Role-Based Sidebar Implementation

## Overview
This implementation adds role-based access control to the restaurant management system, specifically for **<PERSON><PERSON> (<PERSON>)** and **<PERSON><PERSON> (Cashier)** roles with different sidebar menu access.

## Role Definitions

### 1. **<PERSON><PERSON> (<PERSON>)**
- **Sidebar Access**: Only "Order Detail"
- **Purpose**: <PERSON><PERSON> only need to see order details to prepare food

### 2. **<PERSON><PERSON> (Cashier)**
- **Sidebar Access**: "Order" and "Order Detail"
- **Purpose**: Cashiers need to manage orders and see order details

### 3. **Admin**
- **Sidebar Access**: All menu items (unchanged)
- **Purpose**: Full system access

### 4. **User**
- **Sidebar Access**: All menu items except "Users" management
- **Purpose**: Regular user access

## Implementation Details

### Database Changes
1. **Updated user role ENUM** to include 'koki' and 'kasir':
   ```sql
   role ENUM('admin', 'user', 'koki', 'kasir') NOT NULL DEFAULT 'user'
   ```

2. **Added sample users** for testing:
   - Username: `koki`, Password: `password`, Role: `koki`
   - Username: `kasir`, Password: `password`, Role: `kasir`

### Frontend Changes

#### 1. AuthContext Updates
- Added `isKoki()` and `isKasir()` helper functions
- Updated context value to include new role checkers

#### 2. AdminLayout Updates
- Implemented `getMenuItems()` function for role-based menu generation
- **Koki**: Shows only "Order Detail" + "Role Test"
- **Kasir**: Shows "Order" + "Order Detail" + "Role Test"
- **Admin**: Shows all menu items + "Role Test"

#### 3. Route Protection
- Updated `ProtectedRoute` component to support `allowedRoles` array
- Applied role-based protection to individual admin routes:
  - **Order**: Only `admin` and `kasir`
  - **Order Detail**: `admin`, `koki`, and `kasir`
  - **Users**: Only `admin`

### Files Modified

#### Database Files
- `api/users.sql` - Updated role ENUM and added sample users
- `api/setup.php` - Updated table creation and sample data
- `api/register.php` - Updated role validation
- `api/v2/check-db.php` - Updated role ENUM
- `api/test-users.php` - Updated role ENUM

#### Frontend Files
- `src/contexts/AuthContext.jsx` - Added role helper functions
- `src/layouts/AdminLayout.jsx` - Implemented role-based sidebar
- `src/components/ProtectedRoute.jsx` - Added allowedRoles support
- `src/App.jsx` - Updated route protection

#### New Files
- `api/migrate-roles.sql` - SQL migration script
- `api/migrate-roles.php` - PHP migration script
- `src/components/RoleTest.jsx` - Testing component

## Migration Instructions

### For New Installations
1. Run the updated setup script:
   ```
   http://localhost/project-react-resto/api/setup.php
   ```

### For Existing Installations
1. Run the migration script:
   ```
   http://localhost/project-react-resto/api/migrate-roles.php
   ```

## Test Accounts

After migration, you can test with these accounts:

| Username | Password | Role   | Sidebar Access |
|----------|----------|--------|----------------|
| admin    | password | admin  | All menus |
| koki     | password | koki   | Order Detail only |
| kasir    | password | kasir  | Order + Order Detail |

## Testing

1. **Login with different roles** to see sidebar changes
2. **Visit Role Test page** at `/admin/role-test` to verify role functions
3. **Try accessing restricted routes** to test protection

## Usage

1. **Login** with any of the test accounts
2. **Navigate to Admin Panel** (`/admin`)
3. **Observe sidebar** - it will show different menu items based on your role
4. **Test Role Test page** to verify role detection is working correctly

## Security Notes

- All routes are protected with role-based access control
- Users cannot access pages they don't have permission for
- Role validation happens both on frontend (UI) and route level
- Database ENUM ensures only valid roles can be stored

## Future Enhancements

- Add more granular permissions within each role
- Implement role-based field visibility within pages
- Add audit logging for role-based actions
- Create role management interface for admins
