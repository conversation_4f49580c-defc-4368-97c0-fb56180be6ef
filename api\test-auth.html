<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login & Register</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        input[type="text"], input[type="email"], input[type="password"], select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            box-sizing: border-box;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        input:focus, select:focus {
            outline: none;
            border-color: #007bff;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        .register-btn {
            background-color: #28a745;
        }
        .register-btn:hover {
            background-color: #1e7e34;
        }
        .response {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 2px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 2px solid #f5c6cb;
            color: #721c24;
        }
        .test-accounts {
            background-color: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #007bff;
        }
        .quick-fill {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        .quick-fill button {
            width: auto;
            padding: 6px 12px;
            font-size: 12px;
            background-color: #6c757d;
        }
        .quick-fill button:hover {
            background-color: #545b62;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        h2 {
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>🔐 Test Sistem Login & Register</h1>
    
    <div class="test-accounts">
        <h3>📋 Akun Test yang Tersedia:</h3>
        <ul>
            <li><strong>Admin:</strong> username: <code>admin</code>, password: <code>password</code></li>
            <li><strong>Koki:</strong> username: <code>koki</code>, password: <code>password</code></li>
            <li><strong>Kasir:</strong> username: <code>kasir</code>, password: <code>password</code></li>
            <li><strong>User:</strong> username: <code>user</code>, password: <code>password</code></li>
        </ul>
        <p><strong>Catatan:</strong> Untuk register, gunakan username dan email yang berbeda!</p>
    </div>
    
    <div class="container">
        <!-- Login Form -->
        <div class="card">
            <h2>🔑 Test Login</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="loginUsername">Username:</label>
                    <input type="text" id="loginUsername" name="username" value="koki" required>
                </div>
                
                <div class="form-group">
                    <label for="loginPassword">Password:</label>
                    <input type="password" id="loginPassword" name="password" value="password" required>
                </div>
                
                <div class="quick-fill">
                    <button type="button" onclick="fillLogin('admin')">Admin</button>
                    <button type="button" onclick="fillLogin('koki')">Koki</button>
                    <button type="button" onclick="fillLogin('kasir')">Kasir</button>
                    <button type="button" onclick="fillLogin('user')">User</button>
                </div>
                
                <button type="submit" style="margin-top: 15px;">Login</button>
            </form>
            
            <div id="loginResponse"></div>
        </div>
        
        <!-- Register Form -->
        <div class="card">
            <h2>📝 Test Register</h2>
            <form id="registerForm">
                <div class="form-group">
                    <label for="registerName">Nama Lengkap:</label>
                    <input type="text" id="registerName" name="name" value="Test User" required>
                </div>
                
                <div class="form-group">
                    <label for="registerUsername">Username:</label>
                    <input type="text" id="registerUsername" name="username" value="testuser" required>
                </div>
                
                <div class="form-group">
                    <label for="registerEmail">Email:</label>
                    <input type="email" id="registerEmail" name="email" value="<EMAIL>" required>
                </div>
                
                <div class="form-group">
                    <label for="registerPassword">Password:</label>
                    <input type="password" id="registerPassword" name="password" value="password123" required>
                </div>
                
                <div class="form-group">
                    <label for="registerRole">Role (opsional):</label>
                    <select id="registerRole" name="role">
                        <option value="user">User (default)</option>
                        <option value="kasir">Kasir</option>
                        <option value="koki">Koki</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                
                <button type="submit" class="register-btn">Register</button>
            </form>
            
            <div id="registerResponse"></div>
        </div>
    </div>

    <script>
        // Fill login form with test accounts
        function fillLogin(role) {
            document.getElementById('loginUsername').value = role;
            document.getElementById('loginPassword').value = 'password';
        }
        
        // Generate random test data for register
        function generateTestData() {
            const timestamp = Date.now();
            document.getElementById('registerName').value = `Test User ${timestamp}`;
            document.getElementById('registerUsername').value = `testuser${timestamp}`;
            document.getElementById('registerEmail').value = `testuser${timestamp}@example.com`;
        }
        
        // Test Login
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                username: formData.get('username'),
                password: formData.get('password')
            };
            
            try {
                const response = await fetch('login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                document.getElementById('loginResponse').innerHTML = 
                    `<div class="response ${result.success ? 'success' : 'error'}">
                        <strong>Login Response:</strong>\n${JSON.stringify(result, null, 2)}
                    </div>`;
                    
            } catch (error) {
                document.getElementById('loginResponse').innerHTML = 
                    `<div class="response error">
                        <strong>Error:</strong>\n${error.message}
                    </div>`;
            }
        });
        
        // Test Register
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                name: formData.get('name'),
                username: formData.get('username'),
                email: formData.get('email'),
                password: formData.get('password'),
                role: formData.get('role') || 'user'
            };
            
            try {
                const response = await fetch('register.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                document.getElementById('registerResponse').innerHTML = 
                    `<div class="response ${result.success ? 'success' : 'error'}">
                        <strong>Register Response:</strong>\n${JSON.stringify(result, null, 2)}
                    </div>`;
                    
                // If registration successful, generate new test data
                if (result.success) {
                    setTimeout(generateTestData, 2000);
                }
                    
            } catch (error) {
                document.getElementById('registerResponse').innerHTML = 
                    `<div class="response error">
                        <strong>Error:</strong>\n${error.message}
                    </div>`;
            }
        });
        
        // Generate initial test data
        generateTestData();
    </script>
</body>
</html>
