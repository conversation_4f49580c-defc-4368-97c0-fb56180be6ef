import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Card, Badge, Alert } from 'react-bootstrap';

/**
 * Component untuk testing role-based functionality
 */
const RoleTest = () => {
  const { user, isAdmin, isUser, is<PERSON><PERSON>, is<PERSON><PERSON><PERSON> } = useAuth();

  if (!user) {
    return (
      <Alert variant="warning">
        Please login to test role functionality
      </Alert>
    );
  }

  return (
    <div className="container mt-4">
      <h2>Role-Based Access Test</h2>
      
      <Card className="mb-4">
        <Card.Header>
          <h4>Current User Information</h4>
        </Card.Header>
        <Card.Body>
          <p><strong>Name:</strong> {user.name}</p>
          <p><strong>Username:</strong> {user.username}</p>
          <p><strong>Email:</strong> {user.email}</p>
          <p><strong>Role:</strong> <Badge bg="primary">{user.role}</Badge></p>
        </Card.Body>
      </Card>

      <Card className="mb-4">
        <Card.Header>
          <h4>Role Check Functions</h4>
        </Card.Header>
        <Card.Body>
          <div className="row">
            <div className="col-md-3">
              <p>
                <strong>isAdmin():</strong> 
                <Badge bg={isAdmin() ? "success" : "secondary"} className="ms-2">
                  {isAdmin() ? "TRUE" : "FALSE"}
                </Badge>
              </p>
            </div>
            <div className="col-md-3">
              <p>
                <strong>isUser():</strong> 
                <Badge bg={isUser() ? "success" : "secondary"} className="ms-2">
                  {isUser() ? "TRUE" : "FALSE"}
                </Badge>
              </p>
            </div>
            <div className="col-md-3">
              <p>
                <strong>isKoki():</strong> 
                <Badge bg={isKoki() ? "success" : "secondary"} className="ms-2">
                  {isKoki() ? "TRUE" : "FALSE"}
                </Badge>
              </p>
            </div>
            <div className="col-md-3">
              <p>
                <strong>isKasir():</strong> 
                <Badge bg={isKasir() ? "success" : "secondary"} className="ms-2">
                  {isKasir() ? "TRUE" : "FALSE"}
                </Badge>
              </p>
            </div>
          </div>
        </Card.Body>
      </Card>

      <Card className="mb-4">
        <Card.Header>
          <h4>Sidebar Access Rules</h4>
        </Card.Header>
        <Card.Body>
          <div className="row">
            <div className="col-md-4">
              <h5>Admin Role</h5>
              <ul>
                <li>Dashboard</li>
                <li>Kategori</li>
                <li>Menu</li>
                <li>Pelanggan</li>
                <li>Order</li>
                <li>Order Detail</li>
                <li>Pengguna</li>
              </ul>
            </div>
            <div className="col-md-4">
              <h5>Kasir Role</h5>
              <ul>
                <li>Order</li>
                <li>Order Detail</li>
              </ul>
            </div>
            <div className="col-md-4">
              <h5>Koki Role</h5>
              <ul>
                <li>Order Detail</li>
              </ul>
            </div>
          </div>
        </Card.Body>
      </Card>

      <Card>
        <Card.Header>
          <h4>Current User's Sidebar Access</h4>
        </Card.Header>
        <Card.Body>
          {isKoki() && (
            <Alert variant="info">
              <strong>Koki Access:</strong> You can only see Order Detail in the sidebar.
            </Alert>
          )}
          {isKasir() && (
            <Alert variant="warning">
              <strong>Kasir Access:</strong> You can see Order and Order Detail in the sidebar.
            </Alert>
          )}
          {isAdmin() && (
            <Alert variant="success">
              <strong>Admin Access:</strong> You can see all menu items in the sidebar.
            </Alert>
          )}
          {isUser() && (
            <Alert variant="secondary">
              <strong>User Access:</strong> You can see most menu items except Users management.
            </Alert>
          )}
        </Card.Body>
      </Card>
    </div>
  );
};

export default RoleTest;
