<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

// Function to send JSON response
function sendResponse($success, $data = null, $message = '') {
    $response = [
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'debug' => [
            'method' => $_SERVER['REQUEST_METHOD'],
            'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'not set',
            'raw_input' => file_get_contents('php://input')
        ]
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    exit;
}

// Function to get request data
function getRequestData() {
    $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
    
    if (strpos($contentType, 'application/json') !== false) {
        $rawInput = file_get_contents('php://input');
        $data = json_decode($rawInput, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return null;
        }
        
        return $data;
    } else {
        return $_POST;
    }
}

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendResponse(false, null, 'Only POST method allowed');
    }
    
    // Get request data
    $data = getRequestData();
    
    // Log the received data
    error_log('Test login request: ' . json_encode($data));
    
    // Validate data
    if (empty($data)) {
        sendResponse(false, null, 'No data received. Please send JSON data.');
    }
    
    if (!isset($data['username']) && !isset($data['email'])) {
        sendResponse(false, null, 'Username or email is required');
    }
    
    if (!isset($data['password'])) {
        sendResponse(false, null, 'Password is required');
    }
    
    // Connect to database
    $conn = getConnection();
    
    // Determine identifier (username or email)
    $identifier = $data['username'] ?? $data['email'];
    
    // Find user
    $stmt = $conn->prepare("SELECT id, name, username, email, password, role, created_at, updated_at FROM users WHERE username = ? OR email = ?");
    $stmt->bind_param("ss", $identifier, $identifier);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $stmt->close();
        $conn->close();
        sendResponse(false, null, 'User not found with identifier: ' . $identifier);
    }
    
    $user = $result->fetch_assoc();
    $stmt->close();
    
    // Verify password
    if (!password_verify($data['password'], $user['password'])) {
        $conn->close();
        sendResponse(false, null, 'Invalid password for user: ' . $identifier);
    }
    
    // Remove password from user data
    unset($user['password']);
    
    // Generate token
    $token = bin2hex(random_bytes(32));
    
    // Update last login time
    $now = date('Y-m-d H:i:s');
    $stmt = $conn->prepare("UPDATE users SET updated_at = ? WHERE id = ?");
    if ($stmt) {
        $stmt->bind_param("si", $now, $user['id']);
        $stmt->execute();
        $stmt->close();
    }
    
    $conn->close();
    
    // Send success response
    sendResponse(true, [
        'user' => $user,
        'token' => $token
    ], 'Login successful!');
    
} catch (Exception $e) {
    error_log('Test login error: ' . $e->getMessage());
    sendResponse(false, null, 'Error: ' . $e->getMessage());
}
?>
