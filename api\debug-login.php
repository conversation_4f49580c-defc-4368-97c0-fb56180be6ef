<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set headers
header('Content-Type: text/html; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config.php';

echo "<h1>Debug Login System</h1>";

try {
    // Test database connection
    echo "<h2>1. Testing Database Connection</h2>";
    $conn = getConnection();
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Check if users table exists
    echo "<h2>2. Checking Users Table</h2>";
    $result = $conn->query("SHOW TABLES LIKE 'users'");
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✓ Users table exists</p>";
        
        // Check table structure
        $result = $conn->query("DESCRIBE users");
        echo "<h3>Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>✗ Users table does not exist!</p>";
        echo "<p><a href='setup.php'>Run Setup Script</a></p>";
    }
    
    // Check existing users
    echo "<h2>3. Checking Existing Users</h2>";
    $result = $conn->query("SELECT id, name, username, email, role, created_at FROM users ORDER BY id");
    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f2f2f2;'>";
        echo "<th>ID</th><th>Name</th><th>Username</th><th>Email</th><th>Role</th><th>Created</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td><strong>" . htmlspecialchars($row['username']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($row['email']) . "</td>";
            echo "<td>" . htmlspecialchars($row['role']) . "</td>";
            echo "<td>" . htmlspecialchars($row['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>✗ No users found in database!</p>";
        echo "<p><a href='migrate-roles.php'>Run Migration Script</a></p>";
    }
    
    // Test login with koki user
    echo "<h2>4. Testing Login with Koki User</h2>";
    
    // Check if koki user exists
    $stmt = $conn->prepare("SELECT id, name, username, email, password, role FROM users WHERE username = ?");
    $username = 'koki';
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        echo "<p style='color: green;'>✓ Koki user found in database</p>";
        echo "<p><strong>User Details:</strong></p>";
        echo "<ul>";
        echo "<li>ID: " . htmlspecialchars($user['id']) . "</li>";
        echo "<li>Name: " . htmlspecialchars($user['name']) . "</li>";
        echo "<li>Username: " . htmlspecialchars($user['username']) . "</li>";
        echo "<li>Email: " . htmlspecialchars($user['email']) . "</li>";
        echo "<li>Role: " . htmlspecialchars($user['role']) . "</li>";
        echo "</ul>";
        
        // Test password verification
        $testPassword = 'password';
        if (password_verify($testPassword, $user['password'])) {
            echo "<p style='color: green;'>✓ Password verification successful for 'password'</p>";
        } else {
            echo "<p style='color: red;'>✗ Password verification failed for 'password'</p>";
            echo "<p>Stored password hash: " . htmlspecialchars(substr($user['password'], 0, 50)) . "...</p>";
            
            // Try to create a new hash and compare
            $newHash = password_hash('password', PASSWORD_DEFAULT);
            echo "<p>New hash for 'password': " . htmlspecialchars(substr($newHash, 0, 50)) . "...</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Koki user not found!</p>";
        
        // Create koki user
        echo "<h3>Creating Koki User</h3>";
        $password = password_hash('password', PASSWORD_DEFAULT);
        $now = date('Y-m-d H:i:s');
        
        $stmt = $conn->prepare("INSERT INTO users (name, username, email, password, role, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $name = 'Koki';
        $username = 'koki';
        $email = '<EMAIL>';
        $role = 'koki';
        
        $stmt->bind_param("sssssss", $name, $username, $email, $password, $role, $now, $now);
        
        if ($stmt->execute()) {
            echo "<p style='color: green;'>✓ Koki user created successfully</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to create koki user: " . $stmt->error . "</p>";
        }
    }
    
    // Test API endpoint
    echo "<h2>5. Testing API Endpoint</h2>";
    echo "<p>API URL: <code>http://localhost/project-react-resto/project-react-resto/api/login.php</code></p>";
    
    // Create a test login request
    $testData = [
        'username' => 'koki',
        'password' => 'password'
    ];
    
    echo "<h3>Test Login Data:</h3>";
    echo "<pre>" . json_encode($testData, JSON_PRETTY_PRINT) . "</pre>";
    
    // Manual login test
    echo "<h3>Manual Login Test:</h3>";
    $stmt = $conn->prepare("SELECT id, name, username, email, password, role FROM users WHERE username = ? OR email = ?");
    $identifier = 'koki';
    $stmt->bind_param("ss", $identifier, $identifier);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        if (password_verify('password', $user['password'])) {
            unset($user['password']);
            echo "<p style='color: green;'>✓ Manual login test successful!</p>";
            echo "<pre>" . json_encode($user, JSON_PRETTY_PRINT) . "</pre>";
        } else {
            echo "<p style='color: red;'>✗ Manual login test failed - password verification failed</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Manual login test failed - user not found</p>";
    }
    
    $conn->close();
    
    echo "<h2>6. Quick Actions</h2>";
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='setup.php' style='background-color: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px; margin-right: 10px;'>Run Setup</a>";
    echo "<a href='migrate-roles.php' style='background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px; margin-right: 10px;'>Run Migration</a>";
    echo "<a href='../login' style='background-color: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>Go to Login</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
