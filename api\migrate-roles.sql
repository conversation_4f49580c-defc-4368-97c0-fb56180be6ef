-- Migration script to add koki and kasir roles to existing users table
-- Run this script if you already have an existing users table

-- First, check if the table exists and has the old role enum
SELECT COLUMN_TYPE FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'users' 
AND COLUMN_NAME = 'role';

-- Modify the role enum to include new roles
ALTER TABLE `users` 
MODIFY COLUMN `role` ENUM('admin', 'user', 'koki', 'kasir') NOT NULL DEFAULT 'user';

-- Add sample users for testing (only if they don't exist)
INSERT INTO `users` (`name`, `username`, `email`, `password`, `role`, `created_at`, `updated_at`)
SELECT 'Koki', 'koki', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'koki', NOW(), NOW()
WHERE NOT EXISTS (SELECT * FROM `users` WHERE `username` = 'koki');

INSERT INTO `users` (`name`, `username`, `email`, `password`, `role`, `created_at`, `updated_at`)
SELECT 'Kasir', 'kasir', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'kasir', NOW(), NOW()
WHERE NOT EXISTS (SELECT * FROM `users` WHERE `username` = 'kasir');

-- Verify the changes
SELECT id, name, username, email, role, created_at FROM users ORDER BY role, id;

-- Show the updated column definition
SHOW COLUMNS FROM users LIKE 'role';
